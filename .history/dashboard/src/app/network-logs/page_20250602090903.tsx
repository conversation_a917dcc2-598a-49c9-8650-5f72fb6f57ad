'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Activity, 
  Globe, 
  Server, 
  Database, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Filter,
  Download,
  Pause,
  Play,
  RefreshCw
} from 'lucide-react';

interface NetworkLog {
  id: string;
  timestamp: string;
  type: 'API_CALL' | 'DATABASE' | 'EMAIL' | 'WEBSOCKET' | 'HTTP' | 'ERROR';
  method: string;
  url: string;
  status: number;
  responseTime: number;
  size: number;
  userAgent?: string;
  ip?: string;
  error?: string;
  details?: any;
}

interface NetworkStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  totalDataTransferred: number;
  activeConnections: number;
}

export default function NetworkLogsPage() {
  const [logs, setLogs] = useState<NetworkLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<NetworkLog[]>([]);
  const [stats, setStats] = useState<NetworkStats>({
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    totalDataTransferred: 0,
    activeConnections: 0
  });
  const [isLive, setIsLive] = useState(true);
  const [filter, setFilter] = useState({
    type: 'ALL',
    status: 'ALL',
    search: ''
  });
  const [autoScroll, setAutoScroll] = useState(true);
  const logsEndRef = useRef<HTMLDivElement>(null);

  // Simulate real-time network logs
  useEffect(() => {
    if (!isLive) return;

    const interval = setInterval(() => {
      const newLog: NetworkLog = generateMockLog();
      setLogs(prev => {
        const updated = [newLog, ...prev].slice(0, 1000); // Keep last 1000 logs
        return updated;
      });
      
      // Update stats
      setStats(prev => ({
        totalRequests: prev.totalRequests + 1,
        successfulRequests: prev.successfulRequests + (newLog.status < 400 ? 1 : 0),
        failedRequests: prev.failedRequests + (newLog.status >= 400 ? 1 : 0),
        averageResponseTime: (prev.averageResponseTime + newLog.responseTime) / 2,
        totalDataTransferred: prev.totalDataTransferred + newLog.size,
        activeConnections: Math.floor(Math.random() * 50) + 10
      }));
    }, 1000 + Math.random() * 2000); // Random interval between 1-3 seconds

    return () => clearInterval(interval);
  }, [isLive]);

  // Filter logs
  useEffect(() => {
    let filtered = logs;

    if (filter.type !== 'ALL') {
      filtered = filtered.filter(log => log.type === filter.type);
    }

    if (filter.status !== 'ALL') {
      if (filter.status === 'SUCCESS') {
        filtered = filtered.filter(log => log.status < 400);
      } else if (filter.status === 'ERROR') {
        filtered = filtered.filter(log => log.status >= 400);
      }
    }

    if (filter.search) {
      filtered = filtered.filter(log => 
        log.url.toLowerCase().includes(filter.search.toLowerCase()) ||
        log.method.toLowerCase().includes(filter.search.toLowerCase())
      );
    }

    setFilteredLogs(filtered);
  }, [logs, filter]);

  // Auto scroll to bottom
  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [filteredLogs, autoScroll]);

  const generateMockLog = (): NetworkLog => {
    const types: NetworkLog['type'][] = ['API_CALL', 'DATABASE', 'EMAIL', 'WEBSOCKET', 'HTTP'];
    const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    const urls = [
      '/api/properties/search',
      '/api/hud/fmr',
      '/api/email/send',
      '/api/database/query',
      '/api/realestateapi/search',
      '/api/properties/analyze',
      '/api/notifications/send',
      '/api/markets/scan',
      '/api/dashboard/stats',
      '/api/users/activity'
    ];
    const statuses = [200, 201, 400, 401, 403, 404, 500, 502, 503];
    
    const type = types[Math.floor(Math.random() * types.length)];
    const method = methods[Math.floor(Math.random() * methods.length)];
    const url = urls[Math.floor(Math.random() * urls.length)];
    const status = Math.random() > 0.85 ? 
      statuses[Math.floor(Math.random() * 4) + 5] : // Error status
      statuses[Math.floor(Math.random() * 2)]; // Success status
    
    return {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      type,
      method,
      url,
      status,
      responseTime: Math.floor(Math.random() * 2000) + 50,
      size: Math.floor(Math.random() * 50000) + 1000,
      ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
      userAgent: 'Section8Monitor/4.0',
      error: status >= 400 ? 'Request failed' : undefined,
      details: {
        requestId: Math.random().toString(36).substr(2, 9),
        userId: Math.floor(Math.random() * 1000),
        endpoint: url
      }
    };
  };

  const getStatusColor = (status: number) => {
    if (status < 300) return 'text-green-600 bg-green-50';
    if (status < 400) return 'text-blue-600 bg-blue-50';
    if (status < 500) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const getTypeIcon = (type: NetworkLog['type']) => {
    switch (type) {
      case 'API_CALL': return <Globe className="w-4 h-4" />;
      case 'DATABASE': return <Database className="w-4 h-4" />;
      case 'EMAIL': return <Activity className="w-4 h-4" />;
      case 'WEBSOCKET': return <Server className="w-4 h-4" />;
      case 'HTTP': return <Globe className="w-4 h-4" />;
      case 'ERROR': return <AlertCircle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const exportLogs = () => {
    const dataStr = JSON.stringify(filteredLogs, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    const exportFileDefaultName = `network-logs-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Network Activity Logs</h1>
          <p className="text-gray-600 mt-1">Real-time monitoring of all network requests and responses</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={isLive ? "default" : "outline"}
            onClick={() => setIsLive(!isLive)}
            className="flex items-center gap-2"
          >
            {isLive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            {isLive ? 'Pause' : 'Resume'}
          </Button>
          <Button variant="outline" onClick={exportLogs} className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Requests</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalRequests.toLocaleString()}</p>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Successful</p>
                <p className="text-2xl font-bold text-green-600">{stats.successfulRequests.toLocaleString()}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Failed</p>
                <p className="text-2xl font-bold text-red-600">{stats.failedRequests.toLocaleString()}</p>
              </div>
              <AlertCircle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Response</p>
                <p className="text-2xl font-bold text-gray-900">{Math.round(stats.averageResponseTime)}ms</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Data Transfer</p>
                <p className="text-2xl font-bold text-gray-900">{formatBytes(stats.totalDataTransferred)}</p>
              </div>
              <Server className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Connections</p>
                <p className="text-2xl font-bold text-gray-900">{stats.activeConnections}</p>
              </div>
              <Globe className="w-8 h-8 text-indigo-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <Input
                placeholder="Search URL or method..."
                value={filter.search}
                onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}
              />
            </div>
            <Select value={filter.type} onValueChange={(value) => setFilter(prev => ({ ...prev, type: value }))}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Types</SelectItem>
                <SelectItem value="API_CALL">API Calls</SelectItem>
                <SelectItem value="DATABASE">Database</SelectItem>
                <SelectItem value="EMAIL">Email</SelectItem>
                <SelectItem value="WEBSOCKET">WebSocket</SelectItem>
                <SelectItem value="HTTP">HTTP</SelectItem>
                <SelectItem value="ERROR">Errors</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filter.status} onValueChange={(value) => setFilter(prev => ({ ...prev, status: value }))}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Status</SelectItem>
                <SelectItem value="SUCCESS">Success (2xx-3xx)</SelectItem>
                <SelectItem value="ERROR">Error (4xx-5xx)</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={() => setAutoScroll(!autoScroll)}
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Auto Scroll: {autoScroll ? 'On' : 'Off'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Network Logs ({filteredLogs.length})</span>
            <Badge variant={isLive ? "default" : "secondary"}>
              {isLive ? 'Live' : 'Paused'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="max-h-[600px] overflow-auto">
            <div className="space-y-2">
              {filteredLogs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No logs match the current filters
                </div>
              ) : (
                filteredLogs.map((log) => (
                  <div
                    key={log.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-3 flex-1">
                      <div className="flex items-center gap-2">
                        {getTypeIcon(log.type)}
                        <Badge variant="outline" className="text-xs">
                          {log.type}
                        </Badge>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs font-mono">
                          {log.method}
                        </Badge>
                        <span className="text-sm font-mono text-gray-600 truncate max-w-[300px]">
                          {log.url}
                        </span>
                      </div>

                      <Badge className={`text-xs ${getStatusColor(log.status)}`}>
                        {log.status}
                      </Badge>

                      <span className="text-xs text-gray-500">
                        {log.responseTime}ms
                      </span>

                      <span className="text-xs text-gray-500">
                        {formatBytes(log.size)}
                      </span>
                    </div>

                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span>{new Date(log.timestamp).toLocaleTimeString()}</span>
                      {log.error && (
                        <Badge variant="destructive" className="text-xs">
                          Error
                        </Badge>
                      )}
                    </div>
                  </div>
                ))
              )}
              <div ref={logsEndRef} />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
